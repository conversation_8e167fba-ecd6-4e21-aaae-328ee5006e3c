# -*- coding: utf-8 -*-
# Copyright 2023 OpenSPG Authors
#
# Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except
# in compliance with the License. You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software distributed under the License
# is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
# or implied.

import argparse
from kag.bin.base import Command


@Command.register("run_kag_mcp_server")
class RunKagMcpServer(Command):
    def add_to_parser(self, subparsers: argparse._SubParsersAction):  # noqa
        from kag.mcp.server.kag_mcp_server import KagMcpServer

        parser = subparsers.add_parser("mcp-server", help="Run KAG MCP server")
        KagMcpServer.add_options(parser)
        parser.set_defaults(func=self.get_handler())

    @staticmethod
    def handler(args: argparse.Namespace):
        from kag.mcp.server.kag_mcp_server import KagMcpServer

        KagMcpServer.run(args)
