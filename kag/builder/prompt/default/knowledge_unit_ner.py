# -*- coding: utf-8 -*-
# Copyright 2023 OpenSPG Authors
#
# Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except
# in compliance with the License. You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software distributed under the License
# is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
# or implied.

import json
from string import Template
from typing import List

from kag.builder.prompt.default.util import load_NER_data
from kag.common.conf import KAG_PROJECT_CONF
from kag.interface import PromptABC
from knext.schema.client import SchemaClient


@PromptABC.register("knowledge_unit_ner")
class OpenIENERKnowledgeUnitPrompt(PromptABC):
    template_en = """
You are a highly efficient entity extraction system. Based on a given schema that constrains entity categories, your task is to thoroughly analyze source texts and extract all mentioned entities with their names, types, domain ontologies, explanations, standard names, and synonyms. 
Entities must strictly adhere to the schema's predefined types.

If the entity type does not exist, return an empty list. Please respond in JSON list format, where each entity is represented as a list. If there are no entities of a given type, return an empty list. The format is as follows:
[
    {
        "Name": An entity noun/proper noun/gerund from the text with real-world significance (avoid generic terms like 'air conditioner' or pronouns),
        "Category": Entity category strictly following schema classifications (use 'Other' if unclassifiable),
        "Domain Ontology": A taxonomic hierarchy of hypernyms and hyponyms within the entity's disciplinary or professional domain, structured as a hierarchical chain from broad to specific domain-specific concepts. Avoid directly placing the entity itself into the ontology,
        "Description": A descriptive text for each entity generated by integrating contextual information and domain-specific knowledge, avoiding the use of pronouns,
        "Standard Name": The standard name of the entity, in case it is colloquial, abbreviated, or informal in the text,
        "Synonyms": All synonyms of the entity based on knowledge.
    },
    {},
    ……
]

### schema
[ 
$schema
]

### Example:
input:Dr. Zhang Wei (born July 15, 1980), a Chinese geneticist from Beijing, attended the 2023 International Biodiversity Summit in the Alps. He presented findings on Panthera tigris altaica, a critically endangered subspecies. The conference coincided with the enforcement of EU Regulation No. 1143/2023, which restricts trade in endangered species.
output:
[
    {
        "Name": "Zhang Wei",
        "Category": "Person",
        "Domain Ontology": "Human -> Scientist -> Geneticist",
        "Description": "A Chinese geneticist known for attending the 2023 International Biodiversity Summit",
        "Standard Name": "Zhang Wei",
        "Synonyms": []
    },
    {
        "Name": "Alps",
        "Category": "Geographic Location",
        "Domain Ontology": "Physical Geography -> Mountain Range",
        "Description": "A major European mountain range hosting the biodiversity summit",
        "Standard Name": "Alps",
        "Synonyms": ["The Alpine Region"]
    },
    {
        "Name": "2023 International Biodiversity Summit",
        "Category": "Event",
        "Domain Ontology": "Conference -> Scientific Conference -> Environmental Conference",
        "Description": "An international conference focused on biodiversity conservation held in 2023",
        "Standard Name": "2023 International Biodiversity Summit",
        "Synonyms": []
    },
    {
        "Name": "EU Regulation No. 1143/2023",
        "Category": "Policy and Regulation",
        "Domain Ontology": "Legal Framework -> Environmental Law -> Trade Regulation",
        "Description": "Legislation restricting trade of endangered species",
        "Standard Name": "Regulation (EU) No 1143/2023",
        "Synonyms": []
    },
    {
        "Name": "Panthera tigris altaica",
        "Category": "Creature",
        "Domain Ontology": "Animalia -> Chordata -> Mammalia -> Carnivora -> Felidae -> Panthera",
        "Description": "A critically endangered tiger subspecies discussed at the summit",
        "Standard Name": "Panthera tigris altaica",
        "Synonyms": ["Amur Tiger"]
    }
]
### Input:
input:$input
        """

    template_zh = """您是一个高度高效的实体抽取系统。根据给定的约束实体类别的模式（schema），您的任务是全面分析源文本，并提取所有提及到的实体及其对应的名称、类型、领域本体、解释说明、标准名称和同义词。
实体必须严格符合schema中的预定义类型。

如果实体类型不存在，则返回空列表。结果请以JSON列表格式响应，其中每个实体以一个字典表示。如果在给定类型中没有实体，则返回空列表。格式如下：
[
    {
        "名称":一个出现在原文中的实体名词、专有名词或者动名词，具有现实意义,避免例如“空调”等能表达一类共同特性实体的模糊词和代词，
        "类型":指实体的分类，严格根据类别划分到给定Schema中具有的类型，无法划分则划分到Other,
        "领域本体":当前实体所属学科或专业领域的上下位词构成的分类体系，整体是一个由粗到细的分类概念上下位逻辑链，避免将实体直接放入领域本体。
        "解释":结合上下文及你的自身知识，对每个实体生产一段描述性的文本，避免使用代词,
        "标准名":文中的实体名可能是俗称、缩写，结合你所知的知识，给实体一个标准名称,该名称应当尽可能和其他同名实体消除歧义,
        "同义词":根据你的知识列出实体的所有同义词
    },
    {},
    ……
]

### schema
[ 
$schema
]

### Example:
input: 标题: 中共当阳市委党校:当阳市境内学校 (社会/学校)
内容: #中共当阳市委党校:当阳市境内学校\n中共当阳市委党校、当阳市行政学校位于当阳市环城西路129号，是市委、市政府直属的正科级事业单位。\n中文名：中共当阳市委党校。创办时间：1959年12月。现任校长：沈建华。所属地区：当阳市。\n##发展历程\n中共当阳市委党校创办于1959年12月，1960年停办。1972年复校，校址设在原玉阳镇雄风大队娘娘庙。1974年2月，迁至官当（原海军五七干校）。1975年2月，根据县委的指示，增设当阳县五七干校。1978年8月，奉命撤消五七干校。1984年，为落实《中共中央关于实现党校教育正规化的决定》，从官当迁至现址。1985年4月，经省有关部门批准为中专体制县级党校。1994年5月，经市委、市政府批准兼办行政学校，主要负责国家公务员的培训。1998年10月，经省委党校、宜昌市委党校批准，成立学历教育函授站，主要负责在职干部的学历培训。\n##师资力量\n中共当阳市委党校、当阳市行政学校现有教职工(在编在职) 18人，其中，国家公务员12人，专业技术人员5人，工勤人员1人。\n##内设机构\n中共当阳市委党校、当阳市行政学校内设办公室、组教科、社会教育科、总务科等4个科室；校委下设机关党支部和离退休党支部2个党支部。\n##教学设施\n学校现有标准化教室4间（每间50座）、阶梯教室1间（430座）、大教室1间（100座），多媒体教室1间（50座），学员宿舍1栋，有床位150张。常年培训干部500人左右。\n中共当阳市委党校、当阳市行政学校占地54亩，建筑面积9800平方米。校园绿化率、道路硬化率均达到100%，多次被评为当阳市“文明单位”、“花园式学校”，宜昌市党校系统“先进单位”，全省党校系统“九五”评估达标“合格学校”。\n##领导班子\n当阳市委常委、组织部长、市委党校校长：孟 丽\n党委书记、常务副校长：沈建华\n党委委员、副 校 长：万 喆\n党委委员、副 校 长：卢建枝\n党委委员、工会主席：徐海峰。
output:[
    {
        "名称": "中共当阳市委党校",
        "类型": "组织机构",
        "领域本体": "教育 -> 高等教育 -> 党校教育",
        "解释": "中共当阳市委党校是位于湖北省当阳市的一所正科级事业单位，主要负责党员干部的培训和教育。",
        "标准名": "中国共产党当阳市委员会党校",
        "同义词": [
            "当阳市委党校",
            "当阳市行政学校"
        ]
    },
    {
        "名称": "当阳市行政学校",
        "类型": "组织机构",
        "领域本体": "教育 -> 高等教育 -> 行政教育",
        "解释": "当阳市行政学校是位于湖北省当阳市的一所正科级事业单位，主要负责国家公务员的培训。",
        "标准名": "当阳市行政学校",
        "同义词": [
            "中共当阳市委党校"
        ]
    },
    {
        "名称": "沈建华",
        "类型": "人物",
        "领域本体": "人物 -> 教育工作者 -> 党校校长",
        "解释": "沈建华是中共当阳市委党校的党委书记和常务副校长。",
        "标准名": "沈建华",
        "同义词": []
    },
    {
        "名称": "孟丽",
        "类型": "人物",
        "领域本体": "人物 -> 政府官员 -> 组织部长",
        "解释": "孟丽是当阳市委常委、组织部长，并兼任市委党校校长。",
        "标准名": "孟丽",
        "同义词": []
    },
    {
        "名称": "万喆",
        "类型": "人物",
        "领域本体": "人物 -> 教育工作者 -> 党校副校长",
        "解释": "万喆是中共当阳市委党校的党委委员和副校长。",
        "标准名": "万喆",
        "同义词": []
    },
    {
        "名称": "卢建枝",
        "类型": "人物",
        "领域本体": "人物 -> 教育工作者 -> 党校副校长",
        "解释": "卢建枝是中共当阳市委党校的党委委员和副校长。",
        "标准名": "卢建枝",
        "同义词": []
    },
    {
        "名称": "徐海峰",
        "类型": "人物",
        "领域本体": "人物 -> 教育工作者 -> 工会主席",
        "解释": "徐海峰是中共当阳市委党校的党委委员和工会主席。",
        "标准名": "徐海峰",
        "同义词": []
    },
    {
        "名称": "当阳市",
        "类型": "地理位置",
        "领域本体": "地理 -> 行政区划 -> 县级市",
        "解释": "当阳市是湖北省下辖的一个县级市，位于湖北省中部。",
        "标准名": "当阳市",
        "同义词": ["当阳"]
    },
    {
        "名称": "玉阳镇雄风大队娘娘庙",
        "类型": "地理位置",
        "领域本体": "地理 -> 地名 -> 历史地名",
        "解释": "玉阳镇雄风大队娘娘庙是当阳市的一个历史地名，曾是中共当阳市委党校的校址。",
        "标准名": "玉阳镇雄风大队娘娘庙",
        "同义词": []
    },
    {
        "名称": "官当",
        "类型": "地理位置",
        "领域本体": "地理 -> 地名 -> 历史地名",
        "解释": "官当是当阳市的一个历史地名，曾是中共当阳市委党校的校址。",
        "标准名": "官当",
        "同义词": []
    },
    {
        "名称": "当阳县五七干校",
        "类型": "组织机构",
        "领域本体": "教育 -> 历史教育机构 -> 五七干校",
        "解释": "当阳县五七干校是1975年2月根据县委指示增设的一所干校，于1978年8月撤销。",
        "标准名": "当阳县五七干校",
        "同义词": []
    }
]
### Input:
input: $input
"""

    def __init__(self, language: str = "", **kwargs):
        super().__init__(language, **kwargs)
        self.schema = SchemaClient(
            host_addr=KAG_PROJECT_CONF.host_addr, project_id=KAG_PROJECT_CONF.project_id
        ).extract_types(KAG_PROJECT_CONF.language)
        self.template = Template(self.template).safe_substitute(
            schema=json.dumps(self.schema, ensure_ascii=False)
        )

    @property
    def template_variables(self) -> List[str]:
        return ["input"]

    def process_data(self, response: str, **kwargs):
        rsp = response
        if isinstance(rsp, str):
            rsp = json.loads(rsp)
        if isinstance(rsp, dict) and "output" in rsp:
            rsp = rsp["output"]
        return rsp

    def process_en(self, response: dict, **kwargs):
        """ "Name": "2023 International Biodiversity Summit",
        "Category": "Event",
        "Domain Ontology": "Conference -> Scientific Conference -> Environmental Conference",
        "Description": "An international conference focused on biodiversity conservation held in 2023",
        "Standard Name": "2023 International Biodiversity Summit",
        "Synonyms": []"""
        ret = {}
        if "Name" in response.keys():
            ret["name"] = response["Name"]
        if "Category" in response.keys():
            ret["category"] = response["Category"]
        if "Domain Ontology" in response.keys():
            ret["ontology"] = response["Domain Ontology"]
            if isinstance(ret["ontology"], list):
                ret["ontology"] = " -> ".join(ret["ontology"])
        if "Standard Name" in response.keys():
            ret["officialName"] = response["Standard Name"]
        if "Synonyms" in response.keys():
            ret["synonyms"] = response["Synonyms"]
        if "Description" in response.keys():
            ret["description"] = response["Description"]
        return ret

    def process_zh(self, response: dict, **kwargs):
        """{
            "名称": "当阳县五七干校",
            "类型": "组织机构",
            "领域本体": "教育 -> 历史教育机构 -> 五七干校",
            "解释": "当阳县五七干校是1975年2月根据县委指示增设的一所干校，于1978年8月撤销。",
            "标准名": "当阳县五七干校",
            "同义词": []
        }"""
        ret = {}
        if "名称" in response.keys():
            ret["name"] = response["名称"]
        if "类型" in response.keys():
            ret["category"] = response["类型"]
        if "领域本体" in response.keys():
            ret["ontology"] = response["领域本体"]
        if "标准名" in response.keys():
            ret["officialName"] = response["标准名"]
        if "同义词" in response.keys():
            ret["synonyms"] = response["同义词"]
        if "解释" in response.keys():
            ret["description"] = response["解释"]
        return ret

    def parse_response(self, response: str, **kwargs):
        rsp = load_NER_data(response)
        # rsp = self.process_data(response)
        ret = []
        for r in rsp:
            if isinstance(r, list) and len(r):
                r = r[0]
            if not isinstance(r, dict):
                continue
            ret.append(
                self.process_en(r, **kwargs)
                if KAG_PROJECT_CONF.language == "en"
                else self.process_zh(r, **kwargs)
            )
        return ret
