import logging
import re

from kag.interface.solver.base_model import (
    SPOBase,
    SPOEntity,
    SPORelation,
    Identifier,
    TypeInfo,
    LogicNode,
    LFNodeData,
)
from kag.interface.solver.model.schema_utils import SchemaUtils
from kag.common.utils import extract_content_target

logger = logging.getLogger(__name__)


class GetSPONodeData(LFNodeData):
    """Container for retrieved data from a single logical node processing step.

    Attributes:
        sub_question (str): Sub-question being processed
        summary (str): Summary generated by LLM
        chunks (List[str]): Retrieved text chunks
        spo (List[RelationData]): SPO relations retrieved from knowledge graph
    """

    def __init__(self, sub_question=""):
        super().__init__()
        self.sub_question = sub_question  # Sub-question text
        self.summary = ""  # Generated summary
        self.chunks = []  # List of retrieved text chunks
        self.spo = []  # List of SPO relation data objects

    def to_dict(self):
        return {
            "sub_question": self.sub_question,
            "summary": self.summary,
            "chunks": [item.to_dict() for item in self.chunks],
            "spo": str(self.spo),
        }

    def __str__(self):
        """Generate debug-friendly string representation"""
        output_str = f"sub question: {self.sub_question}"

        if self.summary:
            output_str += f"\nsummary:\n{self.summary}"
            return output_str
        if self.chunks:
            output_str += f"\nretrieved chunks:\n{self.chunks}"
        if self.spo:
            output_str += f"\nretrieved spo:\n{self.spo}"

        return output_str

    def __repr__(self) -> str:
        """Generate debug-friendly string representation"""
        output_str = f"sub question: {self.sub_question}"
        if self.chunks:
            output_str += f"\nretrieved chunks:\n{self.chunks}"
        if self.spo:
            output_str += f"\nretrieved spo:\n{self.spo}"
        if self.summary:
            output_str += f"\nsummary:\n{self.summary}"
        return output_str


# get_spg(s, p, o)
@LogicNode.register("get_spo")
class GetSPONode(LogicNode):
    def __init__(self, operator, args):
        super().__init__(operator, args)
        self.s: SPOBase = args.get("s", None)
        self.p: SPOBase = args.get("p", None)
        self.o: SPOBase = args.get("o", None)
        self.op: str = args.get("op", "=")
        self.sub_query = args.get("sub_query", None)
        self.lf_node_res: GetSPONodeData = GetSPONodeData(sub_question=self.sub_query)

    def get_ele_name(self, alias):
        ele = self.args.get(alias, None)
        if ele is None:
            return ""
        if isinstance(ele, SPOEntity):
            return ele.entity_name if ele.entity_name else ""
        return ""

    def __repr__(self):
        params = [f"{k}={str(v)}" for k, v in self.args.items()]
        params_str = ",".join(params)
        return f"{self.operator}({params_str})"

    def to_logical_form_str(self):
        params = [f"{k}={v}" for k, v in self.args.items() if k in ["s", "p", "o"]]
        params_str = ",".join(params)
        return f"{self.operator}({params_str})"

    def to_dsl(self):
        raise NotImplementedError("Subclasses should implement this method.")

    def to_std(self, args):
        for key, value in args.items():
            self.args[key] = value
        self.s = args.get("s", self.s)
        self.p = args.get("p", self.p)
        self.o = args.get("o", self.o)
        self.op = args.get("op", "=")
        self.sub_query = args.get("sub_query", self.sub_query)
        self.lf_node_res.sub_question = self.sub_query

    @staticmethod
    def parse_node(input_str):
        equality_list = re.findall(r"([\w.]+[=<>][^=<>]+)(,|，|$)", input_str)
        if len(equality_list) < 3:
            raise RuntimeError(f"parse {input_str} error not found s,p,o")
        spo_params = [e[0] for e in equality_list[:3]]
        get_spo_node = GetSPONode.parse_node_spo(spo_params)
        if len(equality_list) > 3:
            value_params = [e[0] for e in equality_list[3:]]
            GetSPONode.parse_node_value(get_spo_node, value_params)
        return get_spo_node

    @staticmethod
    def parse_node_spo(spo_params):
        s = None
        p = None
        o = None
        for spo_param in spo_params:
            key, param = spo_param.split("=")
            if key == "s":
                s = SPOEntity.parse_logic_form(param)
            elif key == "o":
                o = SPOEntity.parse_logic_form(param)
            elif key == "p":
                p = SPORelation.parse_logic_form(param)
        if s is None:
            raise RuntimeError(f"parse {str(spo_params)} error not found s")
        if p is None:
            raise RuntimeError(f"parse {str(spo_params)} error not found p")
        if o is None:
            raise RuntimeError(f"parse {str(spo_params)} error not found o")
        return GetSPONode("Retriever", {"s": s, "p": p, "o": o})

    @staticmethod
    def parse_node_value(get_spo_node_op, value_params):
        for value_param in value_params:
            # a.value=123,b.brand=345
            value_pair = re.findall(
                r"(?:[,\s]*(\w+)\.(\w+)(=|<|>|!=)([^,，]+))", value_param
            )
            for key, property, op, value in value_pair:
                node = None
                if key.startswith("s"):
                    node = get_spo_node_op.s
                elif key.startswith("p"):
                    node = get_spo_node_op.p
                elif key.startswith("o"):
                    node = get_spo_node_op.o
                else:
                    raise ValueError(f"invalid node value: {value_param}")
                node.value_list.append([str(property), value, op])


def binary_expr_parse(input_str):
    pattern = re.compile(r"(\w+)=((?:(?!\w+=).)*)")
    matches = pattern.finditer(input_str)
    left_expr = None
    right_expr = None
    op = None
    for match in matches:
        key = match.group(1).strip()
        value = match.group(2).strip().rstrip(",")
        value = value.rstrip("，")
        if key == "left_expr":
            if "," in value:
                left_expr_list = list(set([Identifier(v) for v in value.split(",")]))
            elif "，" in value:
                left_expr_list = list(set([Identifier(v) for v in value.split("，")]))
            else:
                left_expr_list = [Identifier(value)]
            if len(left_expr_list) == 1:
                left_expr = left_expr_list[0]
            else:
                left_expr = left_expr_list
        elif key == "right_expr":
            if value != "":
                right_expr = value
        elif key == "op":
            op = value
    if left_expr is None:
        raise RuntimeError(f"parse {input_str} error not found left_expr")

    if op is None:
        raise RuntimeError(f"parse {input_str} error not found op")
    return {"left_expr": left_expr, "right_expr": right_expr, "op": op}


class MathNode(LogicNode):
    def __init__(self, operator, args):
        super().__init__(operator, args)
        self.content = args.get("content", [])
        self.target = args.get("target", [])
        self.alias_name = args.get("alias_name", "")

    def __str__(self):
        return f"math(content={self.content}, target={self.target})"

    @staticmethod
    def parse_node(input_str, output_name):
        content, target = extract_content_target(input_str)
        if content is None and target is None:
            raise RuntimeError(f"parse {input_str} error not found content/target")
        params_dict = {"alias_name": output_name, "content": content, "target": target}
        return MathNode("Math", params_dict)


class DeduceNode(LogicNode):
    def __init__(self, operator, args):
        super().__init__(operator, args)
        self.ops = args.get("op", [])
        self.content = args.get("content", [])
        self.target = args.get("target", [])
        self.alias_name = args.get("alias_name", "")

    def __str__(self):
        return f"deduce(op={','.join(self.ops)}, content={self.content}, target={self.target})"

    @staticmethod
    def parse_node(input_str, output_name):
        equality_list = re.findall(r"([\w.]+=[^=]+)(,|，|$)", input_str)
        if len(equality_list) < 3:
            raise RuntimeError(f"parse {input_str} error not found op/content/target")
        params = [e[0] for e in equality_list[:3]]
        params_dict = {"alias_name": output_name}
        for param in params:
            key, value = param.split("=")
            if key == "op":
                value = re.sub("'\"`", "", value)
                value = (
                    value.strip()
                    .replace("，", ",")
                    .replace(" ", "")
                    .replace(" ", "")
                    .strip("[")
                    .strip("]")
                    .split(",")
                )
            params_dict[key] = value
        return DeduceNode("Deduce", params_dict)


# get(alias_name)
class GetNode(LogicNode):
    def __init__(self, operator, args):
        super(GetNode, self).__init__(operator, args)
        self.alias_name = args.get("alias_name")
        self.alias_name_set: list = args.get("alias_name_set")
        self.s = args.get("s", None)
        self.s_alias_map: dict = args.get("s_alias_map", None)

    def to_dsl(self):
        raise NotImplementedError("Subclasses should implement this method.")

    @staticmethod
    def parse_node(input_str):
        input_str = input_str.strip()
        input_str = input_str.replace("[", "")
        input_str = input_str.replace("]", "")
        input_args = input_str.split(",")
        input_args = [e.strip().strip("`") for e in input_args]
        return GetNode(
            "Output",
            {
                "alias_name": Identifier(input_args[0]),
                "alias_name_set": [Identifier(e) for e in input_args],
            },
        )


# search_s()
class SearchNode(LogicNode):
    def __init__(self, operator, args):
        super().__init__(operator, args)
        self.s = SPOEntity(None, None, args["type"], None, args["alias"], False)
        self.s.value_list = args["conditions"]

    @staticmethod
    def parse_node(input_str):
        pattern = re.compile(r"[,\s]*s=(\w+):([^,\s]+),(.*)")
        matches = pattern.match(input_str)
        args = dict()
        args["alias"] = matches.group(1)
        args["type"] = matches.group(2)
        if len(matches.groups()) > 2:
            search_condition = dict()
            s_condition = matches.group(3)

            condition_pattern = re.compile(r"(?:[,\s]*(\w+)\.(\w+)=([^,，]+))")
            condition_list = condition_pattern.findall(s_condition)
            for condition in condition_list:
                s_property = condition[1]
                s_value = condition[2]
                s_value = SearchNode.check_value_is_reference(s_value)
                search_condition[s_property] = s_value
            args["conditions"] = search_condition

        return SearchNode("search_s", args)

    @staticmethod
    def check_value_is_reference(value_str):
        if "." in value_str:
            return value_str.split(".")
        return value_str


class ParseLogicForm:
    def __init__(self, schema: SchemaUtils, schema_retrieval):
        self.schema = schema
        self.schema_retrieval = schema_retrieval

    def std_parse_kg_node(self, entity: SPOBase, parsed_entity_set):
        alias_name = entity.alias_name
        if alias_name in parsed_entity_set.keys():
            exist_node = parsed_entity_set[alias_name]
            exist_node.value_list.extend(entity.value_list)
            return parsed_entity_set[alias_name]

        zh_types = entity.get_un_std_entity_type_set()
        std_entity_type_set = []
        if isinstance(entity, SPOEntity):
            for entity_type in zh_types:
                type_info = self.get_node_type_info(entity_type)
                if type_info.std_entity_type is None and self.schema is not None:
                    entity.is_attribute = True
                std_entity_type_set.append(type_info)
        elif isinstance(entity, SPORelation):
            s_type_set = entity.s.type_set
            o_type_set = entity.o.type_set
            for p_unstd_type in zh_types:
                p_std_types = []
                for s_type in s_type_set:
                    for o_type in o_type_set:
                        type_info = TypeInfo()
                        type_info.un_std_entity_type = p_unstd_type

                        def check_type_is_exists(type_name):
                            if type_name is None or type_name == "Entity":
                                return False
                            return True

                        if self.schema is not None:
                            if not check_type_is_exists(o_type.std_entity_type):
                                sp_index = (s_type.un_std_entity_type, p_unstd_type)
                                if sp_index in self.schema.sp_o:
                                    o_candis_set = self.schema.sp_o[sp_index]
                                    for candis in o_candis_set:
                                        spo_zh = f"{s_type.un_std_entity_type}_{p_unstd_type}_{candis}"
                                        type_info.std_entity_type = (
                                            self.schema.get_spo_with_p(
                                                self.schema.spo_zh_en[spo_zh]
                                            )
                                        )
                                        break
                            if (
                                not type_info.std_entity_type
                                and not check_type_is_exists(s_type.std_entity_type)
                            ):
                                op_index = (o_type.un_std_entity_type, p_unstd_type)
                                if op_index in self.schema.op_s:
                                    s_candis_set = self.schema.op_s[op_index]
                                    for candis in s_candis_set:
                                        spo_zh = f"{candis}_{p_unstd_type}_{o_type.un_std_entity_type}"
                                        type_info.std_entity_type = (
                                            self.schema.get_spo_with_p(
                                                self.schema.spo_zh_en[spo_zh]
                                            )
                                        )
                                        break

                            if (
                                not type_info.std_entity_type
                                and check_type_is_exists(s_type.std_entity_type)
                                and check_type_is_exists(o_type.std_entity_type)
                            ):
                                so_index = (
                                    s_type.un_std_entity_type,
                                    o_type.un_std_entity_type,
                                )
                                if so_index not in self.schema.so_p:
                                    so_index = (
                                        o_type.un_std_entity_type,
                                        s_type.un_std_entity_type,
                                    )
                                candis_set = self.schema.so_p[so_index]
                                for p_candis in candis_set:
                                    if p_candis == p_unstd_type:
                                        spo_zh = f"{s_type.un_std_entity_type}_{p_candis}_{o_type.un_std_entity_type}"
                                        type_info.std_entity_type = (
                                            self.schema.get_spo_with_p(
                                                self.schema.spo_zh_en[spo_zh]
                                            )
                                        )

                            if not type_info.std_entity_type:
                                # maybe a property
                                s_attr_zh_en = self.schema.attr_zh_en_by_label.get(
                                    s_type.std_entity_type, []
                                )
                                if s_attr_zh_en and p_unstd_type in s_attr_zh_en:
                                    type_info.std_entity_type = s_attr_zh_en[
                                        p_unstd_type
                                    ]
                                if not type_info.std_entity_type:
                                    o_attr_zh_en = self.schema.attr_zh_en_by_label.get(
                                        o_type.std_entity_type, []
                                    )
                                    if o_attr_zh_en and p_unstd_type in o_attr_zh_en:
                                        type_info.std_entity_type = o_attr_zh_en[
                                            p_unstd_type
                                        ]

                        if type_info.std_entity_type:
                            p_std_types.append(type_info)
                if len(p_std_types) == 0:
                    type_info = TypeInfo()
                    type_info.un_std_entity_type = p_unstd_type
                    p_std_types.append(type_info)
                std_entity_type_set += p_std_types

        entity.type_set = std_entity_type_set
        parsed_entity_set[alias_name] = entity

        return entity

    def std_parse_node(self, entity: SPOEntity, parsed_entity_set):
        alias_name = entity.alias_name
        if alias_name in parsed_entity_set.keys():
            exist_node = parsed_entity_set[alias_name]
            exist_node.value_list.extend(entity.value_list)
            return parsed_entity_set[alias_name]

        zh_types = entity.get_un_std_entity_type_set()
        std_entity_type_set = []
        for entity_type in zh_types:
            type_info = self.get_node_type_info(entity_type)
            if type_info.std_entity_type is None and self.schema is not None:
                entity.is_attribute = True
            std_entity_type_set.append(type_info)
        entity.type_set = std_entity_type_set
        parsed_entity_set[alias_name] = entity
        return entity

    def std_parse_edge(self, edge: SPORelation, parsed_entity_set):
        alias_name = edge.alias_name
        if alias_name in parsed_entity_set.keys():
            return parsed_entity_set[alias_name]
        zh_types = edge.get_un_std_entity_type_set()
        std_edge_type_set = []
        for entity_type in zh_types:
            type_info = self.get_edge_type_info(entity_type)
            if type_info.std_entity_type is None and self.schema is not None:
                edge.is_attribute = True
            std_edge_type_set.append(type_info)
        edge.type_set = std_edge_type_set

        parsed_entity_set[alias_name] = edge
        return edge

    def std_logic_form(self, node: LogicNode, sub_query, parsed_entity_set=None):
        if parsed_entity_set is None:
            parsed_entity_set = {}
        if isinstance(node, GetSPONode):
            s_node = self.std_parse_kg_node(node.s, parsed_entity_set)
            o_node = self.std_parse_kg_node(node.o, parsed_entity_set)
            node.p.s = s_node
            node.p.o = o_node
            p_node = self.std_parse_kg_node(node.p, parsed_entity_set)
            node.to_std(
                {
                    "s": s_node,
                    "p": p_node,
                    "o": o_node,
                    "sub_query": sub_query,
                }
            )
        return node

    def parse_logic_form(
        self, input_str: str, parsed_entity_set={}, sub_query=None, query=None
    ):
        match = re.match(
            r"(\w+)[\(\（](.*)[\)\）](->)?(.*)?", input_str.strip().replace("\n", " ")
        )
        if not match:
            raise RuntimeError(f"parse logic form error {input_str}")
        if len(match.groups()) == 4:
            operator, args_str, _, output_name = match.groups()
        else:
            operator, args_str = match.groups()
            output_name = None

        operator = operator.lower()
        if operator in ["get", "output"]:
            node: GetNode = GetNode.parse_node(args_str)
            if node.alias_name in parsed_entity_set.keys():
                s = parsed_entity_set[node.alias_name]
                node.s = s
        elif operator in ["get_spo", "retrieval", "retriever"]:
            node: GetSPONode = GetSPONode.parse_node(args_str)
            node = self.std_logic_form(
                node=node, sub_query=sub_query, parsed_entity_set=parsed_entity_set
            )
        elif operator in ["math"]:
            node: MathNode = MathNode.parse_node(args_str, output_name)
        elif operator in ["deduce"]:
            node: DeduceNode = DeduceNode.parse_node(args_str, output_name)
        elif operator in ["search_s"]:
            node: SearchNode = SearchNode.parse_node(args_str)
            self.std_parse_node(node.s, parsed_entity_set)
        else:
            raise NotImplementedError(f"not impl {input_str}")

        node.to_std({"sub_query": sub_query, "init_query": query})

        return node

    def parse_logic_form_set(
        self, input_str_set: list, sub_querys: list, question: str
    ):
        parsed_cached_map = {}
        parsed_node = []
        for i, input_str in enumerate(input_str_set):
            if sub_querys and i < len(sub_querys):
                sub_query = sub_querys[i]
            else:
                sub_query = None
            if not sub_query:
                raise RuntimeError(f"sub query is empty {sub_query} {input_str}")
            logic_node = self.parse_logic_form(
                input_str, parsed_cached_map, sub_query=sub_query, query=question
            )
            parsed_node.append(logic_node)
        return parsed_node

    def std_node_type_name(self, type_name):
        if self.schema_retrieval is None:
            return type_name
        try:
            search_entity_labels = self.schema_retrieval.retrieval_entity(
                SPOEntity(entity_name=type_name)
            )
            if len(search_entity_labels) > 0:
                return search_entity_labels[0].name
        except Exception as e:
            logger.warning(f"parse node {type_name} error {e}", exc_info=True)
        return type_name

    def get_edge_type_en_by_name(self, type_name):
        if self.schema is None:
            return None
        if type_name in self.schema.edge_en_zh.keys():
            return type_name
        return self.schema.edge_zh_en.get(type_name, None)

    def get_node_type_en_by_name(self, type_name):
        if self.schema is None:
            return type_name
        if type_name in self.schema.node_en_zh.keys():
            return type_name
        return self.schema.node_zh_en.get(type_name, None)

    def get_node_type_zh_by_name(self, type_name):
        if self.schema is None:
            return type_name
        if type_name in self.schema.node_zh_en.keys():
            return type_name
        return self.schema.node_en_zh.get(type_name, None)

    def get_node_type_info(self, type_name):
        zh = self.get_node_type_zh_by_name(type_name)
        en = self.get_node_type_en_by_name(type_name)
        if zh == en:
            en = self.std_node_type_name(type_name)
        type_info = TypeInfo()
        type_info.std_entity_type = en
        type_info.un_std_entity_type = zh
        if type_info.un_std_entity_type is None:
            type_info.un_std_entity_type = type_name
        return type_info

    def get_edge_type_info(self, type_name):
        # Edge is not standardized currently
        type_info = TypeInfo()
        type_info.std_entity_type = self.get_edge_type_en_by_name(type_name)
        type_info.un_std_entity_type = type_name
        return type_info


def extract_steps_and_actions(text):
    # 提取 Step 和紧跟的 Action
    step_pattern = re.compile(
        r"([Ss][Tt][Ee][Pp]\d+):\s*(.*?)(?=\s*[Aa][Cc][Tt][Ii][Oo][Nn]\d+:|\s*[Ss][Tt][Ee][Pp]\d+:|$)",
        re.DOTALL,
    )
    action_pattern = re.compile(
        r"([Aa][Cc][Tt][Ii][Oo][Nn]\d+):\s*(.*?)(?=\s*[Ss][Tt][Ee][Pp]\d+:|$)",
        re.DOTALL,
    )

    steps = []
    actions = []

    for match in step_pattern.finditer(text):
        step_name = match.group(1)
        step_content = match.group(2).strip()
        steps.append((step_name, step_content))

    for match in action_pattern.finditer(text):
        action_name = match.group(1)
        action_content = match.group(2).strip()
        actions.append((action_name, action_content))

    # 将 Step 和 Action 按位置对应起来
    for i in range(len(steps)):
        step_name, step_content = steps[i]
        if i < len(actions):
            action_name, action_content = actions[i]
        else:
            action_name = action_content = None
        return step_content, step_name, action_content, action_name

    return None, None, None, None


def parse_logic_form_with_str(response):
    logger.debug(f"logic form:{response}")
    _output_string = response.replace("：", ":")
    _output_string = _output_string.strip()
    sub_querys = []
    logic_forms = []
    current_sub_query = ""
    for line in _output_string.split("\n"):
        if line.startswith("Step"):
            step_query, _, action, _ = extract_steps_and_actions(line)
            if step_query is not None:
                sub_querys.append(step_query)
                current_sub_query = step_query.strip()
                if current_sub_query == "":
                    raise RuntimeError(f"{line} is not step query")
            if action is not None:
                logic_forms.append(action)
        elif line.startswith("Action"):
            logic_forms_regex = re.search(r"Action\d+:(.*)", line)
            if logic_forms_regex:
                logic_forms.append(logic_forms_regex.group(1))
                if len(logic_forms) - len(sub_querys) == 1:
                    sub_querys.append(current_sub_query)
    if len(sub_querys) != len(logic_forms):
        raise RuntimeError(
            f"sub query not equal logic form num {len(sub_querys)} != {len(logic_forms)}"
        )
    return sub_querys, logic_forms


if __name__ == "__main__":
    d = "Step1:  What continent is Panama in? Action1:Retrieval(s=s1:sovereignState[`Panama`], p=p1:continent, o=o1:geographicRegion) "
    d = "Step1:  What continent is Panama in? Action1: Retrieval(s=s1:sovereignState[`Panama`], p=p1:continent, o=o1:geographicRegion) "
    # d = 'Step1:  What continent is Panama in? '

    print(extract_steps_and_actions(d))
    parse_logic_form_with_str(d)
