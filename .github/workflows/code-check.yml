name: CI

on:
  push:
  pull_request:
  workflow_dispatch:
  repository_dispatch:
    types: [my_event]
jobs:
  format-check:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.10'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install pre-commit pytest pytest-cov 
          pip install -r requirements.txt
          pip install -e .
          pip install black==24.10.0
      - name: Run pre-commit
        run: pre-commit run --all-files

      # - name: Run unit tests
      #   run: pushd tests/unit && pytest && popd
        
